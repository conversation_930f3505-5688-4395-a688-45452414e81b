on_actions = {
	# Prevent AI from sending faction invitations while at war
	on_daily = {
		effect = {
			every_country = {
				limit = {
					is_ai = yes
					has_war = yes
				}
				# Add AI strategy to prevent all diplomatic actions while at war
				add_ai_strategy = {
					type = alliance
					value = -50000
				}
				add_ai_strategy = {
					type = befriend
					value = -50000
				}
				add_ai_strategy = {
					type = antagonize
					value = 50000
				}
			}
		}
	}

	# Apply restrictions when war starts
	on_war_relation_added = {
		effect = {
			if = {
				limit = { is_ai = yes }
				add_ai_strategy = {
					type = alliance
					value = -50000
				}
				add_ai_strategy = {
					type = befriend
					value = -50000
				}
			}
			FROM = {
				if = {
					limit = { is_ai = yes }
					add_ai_strategy = {
						type = alliance
						value = -50000
					}
					add_ai_strategy = {
						type = befriend
						value = -50000
					}
				}
			}
		}
	}

	# Remove restrictions when war ends
	on_war_relation_removed = {
		effect = {
			if = {
				limit = {
					is_ai = yes
					has_war = no
				}
				remove_ai_strategy = {
					type = alliance
				}
				remove_ai_strategy = {
					type = befriend
				}
				remove_ai_strategy = {
					type = antagonize
				}
			}
			FROM = {
				if = {
					limit = {
						is_ai = yes
						has_war = no
					}
					remove_ai_strategy = {
						type = alliance
					}
					remove_ai_strategy = {
						type = befriend
					}
					remove_ai_strategy = {
						type = antagonize
					}
				}
			}
		}
	}
}
