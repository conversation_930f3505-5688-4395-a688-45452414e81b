on_actions = {
	# Prevent AI from sending faction invitations while at war
	on_daily = {
		effect = {
			every_country = {
				limit = {
					is_ai = yes
					has_war = yes
					is_faction_leader = yes
				}
				# Add AI strategy to prevent faction invitations while at war
				add_ai_strategy = {
					type = alliance
					value = -10000
				}
				add_ai_strategy = {
					type = befriend
					value = -10000
				}
			}
		}
	}
	
	# Apply restrictions when war starts
	on_war_relation_added = {
		effect = {
			if = {
				limit = { 
					is_ai = yes 
					is_faction_leader = yes
				}
				add_ai_strategy = {
					type = alliance
					value = -10000
				}
			}
			FROM = {
				if = {
					limit = { 
						is_ai = yes 
						is_faction_leader = yes
					}
					add_ai_strategy = {
						type = alliance
						value = -10000
					}
				}
			}
		}
	}
}
