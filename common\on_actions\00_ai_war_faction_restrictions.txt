# AI War Faction Restrictions On Actions
# Prevents AI from joining factions while at war

on_actions = {
	# Check daily if countries should have faction restrictions
	on_daily = {
		effect = {
			every_country = {
				limit = {
					is_ai = yes
					has_war = yes
				}
				# Apply massive penalties to faction joining while at war
				add_ai_strategy = {
					type = alliance
					id = "faction_joining_restriction"
					value = -10000
				}
				add_ai_strategy = {
					type = befriend
					id = "faction_befriend_restriction" 
					value = -10000
				}
			}
			every_country = {
				limit = {
					is_ai = yes
					has_war = no
				}
				# Remove restrictions when not at war
				remove_ai_strategy = {
					type = alliance
					id = "faction_joining_restriction"
				}
				remove_ai_strategy = {
					type = befriend
					id = "faction_befriend_restriction"
				}
			}
		}
	}
	
	# Apply restrictions when war starts
	on_war_relation_added = {
		effect = {
			if = {
				limit = { is_ai = yes }
				add_ai_strategy = {
					type = alliance
					id = "faction_joining_restriction"
					value = -10000
				}
			}
			FROM = {
				if = {
					limit = { is_ai = yes }
					add_ai_strategy = {
						type = alliance
						id = "faction_joining_restriction"
						value = -10000
					}
				}
			}
		}
	}
	
	# Remove restrictions when war ends
	on_war_relation_removed = {
		effect = {
			if = {
				limit = { 
					is_ai = yes 
					has_war = no
				}
				remove_ai_strategy = {
					type = alliance
					id = "faction_joining_restriction"
				}
			}
			FROM = {
				if = {
					limit = { 
						is_ai = yes 
						has_war = no
					}
					remove_ai_strategy = {
						type = alliance
						id = "faction_joining_restriction"
					}
				}
			}
		}
	}
}
